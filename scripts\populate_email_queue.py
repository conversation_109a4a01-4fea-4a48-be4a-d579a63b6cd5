#!/usr/bin/env python3
"""
Script to populate the email_queue with initial cold emails

This script reads recipient emails from a JSON file and schedules them across
multiple sender accounts with daily limits and round-robin distribution.
"""
import json
import sys
import datetime
from pathlib import Path
from typing import List, Dict, Any, <PERSON><PERSON>

# Add the parent directory to the path so we can import from app
sys.path.append(str(Path(__file__).parent.parent))

from app.database.supabase_client import db
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('populate_email_queue')

# ============================================================================
# CONFIGURATION PARAMETERS - MODIFY THESE AS NEEDED
# ============================================================================

# List of sender email addresses (must exist in email_accounts table)
accounts = [
    "<EMAIL>",
    # Add more sender accounts here as needed
    # "<EMAIL>",
    # "<EMAIL>",
]

# For testing with multiple accounts (uncomment to test):
# accounts = [
#     "<EMAIL>",
#     "<EMAIL>",  # This won't exist, just for demo
# ]

# Daily limits per account [day1, day2, day3, day4+]
# Day 1: max 3 emails per account
# Day 2: max 5 emails per account
# Day 3: max 7 emails per account
# Day 4 and beyond: max 10 emails per account
daily_limits = [3, 5, 7, 10]

# Whether to use production emails (True) or warm-up emails (False)
production_mails = True

# Path to the JSON file containing recipient emails
emails_json_file = "emails.json"

# Enable dry-run mode to preview scheduling without writing to DB
dry_run = True

# Starting date for scheduling (None = today)
start_date = None  # Will default to today if None

# ============================================================================
# SCRIPT IMPLEMENTATION
# ============================================================================

def validate_sender_accounts(sender_emails: List[str]) -> List[Dict[str, Any]]:
    """
    Validate that sender email accounts exist in the database.

    Args:
        sender_emails: List of sender email addresses

    Returns:
        List of validated account records
    """
    logger.info(f"Validating {len(sender_emails)} sender accounts...")

    validated_accounts = []

    for email in sender_emails:
        try:
            # Get account by email
            response = db.client.table('email_accounts').select('*').eq('email', email).eq('active', True).execute()
            accounts_data = db._handle_response(response)

            if accounts_data and len(accounts_data) > 0:
                account = accounts_data[0]
                validated_accounts.append(account)
                logger.info(f"✓ Found account: {email} (ID: {account['id']})")
            else:
                logger.error(f"✗ Account not found or inactive: {email}")
                return None

        except Exception as e:
            logger.error(f"Error validating account {email}: {str(e)}")
            return None

    logger.info(f"Successfully validated {len(validated_accounts)} accounts")
    return validated_accounts

def load_recipient_emails(json_file: str) -> List[str]:
    """
    Load recipient emails from JSON file.

    Args:
        json_file: Path to JSON file containing recipient emails

    Returns:
        List of recipient email addresses
    """
    json_path = Path(__file__).parent.parent / json_file

    if not json_path.exists():
        logger.error(f"Emails file not found: {json_path}")
        return None

    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Extract email addresses from the JSON structure
        emails = []
        for item in data:
            if isinstance(item, dict) and 'email' in item:
                emails.append(item['email'])
            elif isinstance(item, str):
                emails.append(item)

        logger.info(f"Loaded {len(emails)} recipient emails from {json_file}")
        return emails

    except Exception as e:
        logger.error(f"Error loading emails from {json_file}: {str(e)}")
        return None

def get_email_content_templates(is_warm_up: bool) -> List[Dict[str, Any]]:
    """
    Get email content templates for initial emails (follow_up = 0).

    Args:
        is_warm_up: Whether to get warm-up or production templates

    Returns:
        List of email content templates
    """
    try:
        response = db.client.table('email_content').select('*').eq('is_warm_up', is_warm_up).eq('follow_up', 0).execute()
        templates = db._handle_response(response)

        if templates and len(templates) > 0:
            logger.info(f"Found {len(templates)} {'warm-up' if is_warm_up else 'production'} email templates")
            return templates
        else:
            logger.error(f"No {'warm-up' if is_warm_up else 'production'} email templates found")
            return None

    except Exception as e:
        logger.error(f"Error fetching email templates: {str(e)}")
        return None



def get_daily_limit(day_index: int, limits: List[int]) -> int:
    """
    Get the daily limit for a specific day.

    Args:
        day_index: Day index (0-based)
        limits: List of daily limits

    Returns:
        Daily limit for the day
    """
    if day_index < len(limits):
        return limits[day_index]
    else:
        # Use the last limit as default for days beyond the specified limits
        return limits[-1]

def count_scheduled_emails_for_day(account_id: str, date: datetime.date) -> int:
    """
    Count how many emails are already scheduled for an account on a specific day.

    Args:
        account_id: Account ID
        date: Date to check

    Returns:
        Number of emails scheduled for that day
    """
    try:
        # Calculate start and end of the day
        start_time = datetime.datetime.combine(date, datetime.time.min).replace(tzinfo=datetime.timezone.utc)
        end_time = datetime.datetime.combine(date, datetime.time.max).replace(tzinfo=datetime.timezone.utc)

        response = db.client.table('email_queue').select('id').eq('account_id', account_id).eq('status', 'scheduled').gte('scheduled_time', start_time.isoformat()).lte('scheduled_time', end_time.isoformat()).execute()

        result = db._handle_response(response)
        count = len(result) if result else 0

        logger.debug(f"Account {account_id} has {count} emails scheduled for {date}")
        return count

    except Exception as e:
        logger.error(f"Error counting scheduled emails for {account_id} on {date}: {str(e)}")
        return 0

def schedule_emails(accounts: List[Dict[str, Any]], recipients: List[str],
                   templates: List[Dict[str, Any]], limits: List[int],
                   start_date: datetime.date, dry_run: bool = True) -> Dict[str, Any]:
    """
    Schedule emails across accounts with daily limits.

    Args:
        accounts: List of validated sender accounts
        recipients: List of recipient email addresses
        templates: List of email content templates
        limits: Daily limits per account
        start_date: Starting date for scheduling
        dry_run: If True, only preview without writing to DB

    Returns:
        Dictionary with scheduling statistics
    """
    logger.info(f"Starting email scheduling for {len(recipients)} recipients across {len(accounts)} accounts")

    stats = {
        'total_recipients': len(recipients),
        'total_accounts': len(accounts),
        'emails_scheduled': 0,
        'emails_per_account': {},
        'emails_per_day': {},
        'errors': [],
        'database_records': []  # Store exact email_queue records that would be inserted
    }

    # Initialize stats
    for account in accounts:
        stats['emails_per_account'][account['email']] = 0

    current_date = start_date
    current_account_index = 0
    template_index = 0

    for i, recipient_email in enumerate(recipients):
        try:

            # Round-robin account selection
            account = accounts[current_account_index % len(accounts)]
            account_id = account['id']
            account_email = account['email']

            # Find a suitable day for this email
            scheduled = False
            day_offset = 0

            while not scheduled and day_offset < 30:  # Limit to 30 days ahead
                check_date = current_date + datetime.timedelta(days=day_offset)
                daily_limit = get_daily_limit(day_offset, limits)

                # Count existing emails for this account on this day
                if not dry_run:
                    current_count = count_scheduled_emails_for_day(account_id, check_date)
                else:
                    # For dry run, simulate counting
                    day_key = f"{account_email}_{check_date}"
                    current_count = stats['emails_per_day'].get(day_key, 0)

                if current_count < daily_limit:
                    # Schedule the email for this day
                    scheduled_time = datetime.datetime.combine(
                        check_date,
                        datetime.time(9, 0)  # Schedule for 9 AM
                    ).replace(tzinfo=datetime.timezone.utc)

                    # Add some random minutes to spread emails throughout the day
                    import random
                    random_minutes = random.randint(0, 480)  # 0-8 hours (9 AM - 5 PM)
                    scheduled_time += datetime.timedelta(minutes=random_minutes)

                    # Select email template (round-robin)
                    template = templates[template_index % len(templates)]
                    template_index += 1

                    if dry_run:
                        logger.info(f"[DRY RUN] Would schedule email {i+1}: {recipient_email} -> {account_email} at {scheduled_time}")

                        # Create the exact database record that would be inserted
                        email_record = {
                            'account_id': account_id,
                            'recipient_email': recipient_email,
                            'content_id': template['id'],
                            'scheduled_time': scheduled_time.isoformat(),
                            'status': 'scheduled',
                            'follow_up': 0,
                            'is_follow_up': False
                        }

                        # Add additional metadata for dry run analysis
                        email_record_with_meta = {
                            **email_record,
                            '_meta': {
                                'recipient_email': recipient_email,
                                'sender_email': account_email,
                                'template_subject': template.get('subject', 'N/A'),
                                'template_body_preview': template.get('body', '')[:100] + '...' if template.get('body') else 'N/A',
                                'day_number': day_offset + 1,
                                'daily_limit': daily_limit,
                                'scheduled_date': check_date.isoformat(),
                                'scheduled_time_local': scheduled_time.strftime('%Y-%m-%d %H:%M:%S UTC')
                            }
                        }

                        stats['database_records'].append(email_record_with_meta)

                        # Update dry run stats
                        day_key = f"{account_email}_{check_date}"
                        stats['emails_per_day'][day_key] = stats['emails_per_day'].get(day_key, 0) + 1
                    else:
                        # Actually schedule the email
                        success = schedule_single_email(
                            account_id, recipient_email, template['id'],
                            scheduled_time, account_email
                        )

                        if not success:
                            stats['errors'].append(f"Failed to schedule email for {recipient_email}")
                            continue

                    # Update statistics
                    stats['emails_scheduled'] += 1
                    stats['emails_per_account'][account_email] += 1
                    scheduled = True

                else:
                    # This day is full for this account, try next day
                    day_offset += 1

            if not scheduled:
                stats['errors'].append(f"Could not find available slot for {recipient_email} within 30 days")

            # Move to next account
            current_account_index += 1

        except Exception as e:
            error_msg = f"Error scheduling email for {recipient_email}: {str(e)}"
            logger.error(error_msg)
            stats['errors'].append(error_msg)

    return stats

def schedule_single_email(account_id: str, recipient_email: str, content_id: str,
                         scheduled_time: datetime.datetime, account_email: str) -> bool:
    """
    Schedule a single email in the database.

    Args:
        account_id: Sender account ID
        recipient_email: Recipient email address
        content_id: Email content template ID
        scheduled_time: When to send the email
        account_email: Sender email (for logging)

    Returns:
        True if successful, False otherwise
    """
    try:
        email_data = {
            'account_id': account_id,
            'recipient_email': recipient_email,
            'content_id': content_id,
            'scheduled_time': scheduled_time.isoformat(),
            'status': 'scheduled',
            'follow_up': 0,
            'is_follow_up': False
        }

        response = db.client.table('email_queue').insert(email_data).execute()
        result = db._handle_response(response)

        if result and len(result) > 0:
            email_id = result[0]['id']
            logger.info(f"Scheduled email {email_id}: {recipient_email} -> {account_email} at {scheduled_time}")
            return True
        else:
            logger.error(f"Failed to schedule email: {recipient_email} -> {account_email}")
            return False

    except Exception as e:
        logger.error(f"Error scheduling email {recipient_email} -> {account_email}: {str(e)}")
        return False

def save_dry_run_results(stats: Dict[str, Any], filename: str = "dry_run_results.json"):
    """
    Save dry run results to a JSON file with exact database records.

    Args:
        stats: Scheduling statistics
        filename: Output filename
    """
    try:
        output_path = Path(__file__).parent.parent / filename

        # Convert datetime objects to strings for JSON serialization
        json_stats = {
            'summary': {
                'total_recipients': stats['total_recipients'],
                'total_accounts': stats['total_accounts'],
                'emails_scheduled': stats['emails_scheduled'],
                'emails_per_account': stats['emails_per_account'],
                'emails_per_day': {str(k): v for k, v in stats['emails_per_day'].items()},
                'errors': stats['errors'],
                'generated_at': datetime.datetime.now().isoformat()
            },
            'database_records': {
                'email_queue_inserts': stats.get('database_records', []),
                'total_email_queue_records': len(stats.get('database_records', [])),
                'description': 'These are the exact records that would be inserted into the email_queue table'
            }
        }

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_stats, f, indent=2, ensure_ascii=False)

        logger.info(f"Dry run results saved to: {output_path}")
        logger.info(f"Saved {len(stats.get('database_records', []))} email_queue records for preview")

    except Exception as e:
        logger.error(f"Error saving dry run results: {str(e)}")

def print_summary(stats: Dict[str, Any]):
    """
    Print a summary of the scheduling results.

    Args:
        stats: Scheduling statistics
    """
    logger.info("=" * 60)
    logger.info("EMAIL SCHEDULING SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Total recipients: {stats['total_recipients']}")
    logger.info(f"Total accounts: {stats['total_accounts']}")
    logger.info(f"Emails scheduled: {stats['emails_scheduled']}")
    logger.info(f"Errors: {len(stats['errors'])}")

    logger.info("\nEmails per account:")
    for account, count in stats['emails_per_account'].items():
        logger.info(f"  {account}: {count} emails")

    if stats['emails_per_day']:
        logger.info("\nEmails per day:")
        for day_key, count in sorted(stats['emails_per_day'].items()):
            logger.info(f"  {day_key}: {count} emails")

    if stats['errors']:
        logger.info(f"\nErrors ({len(stats['errors'])}):")
        for error in stats['errors'][:10]:  # Show first 10 errors
            logger.info(f"  - {error}")
        if len(stats['errors']) > 10:
            logger.info(f"  ... and {len(stats['errors']) - 10} more errors")

    logger.info("=" * 60)

def main():
    """
    Main function to populate the email queue.
    """
    logger.info("Starting email queue population script")
    logger.info(f"Configuration:")
    logger.info(f"  - Accounts: {accounts}")
    logger.info(f"  - Daily limits: {daily_limits}")
    logger.info(f"  - Production mails: {production_mails}")
    logger.info(f"  - Emails JSON file: {emails_json_file}")
    logger.info(f"  - Dry run: {dry_run}")
    logger.info(f"  - Start date: {start_date or 'today'}")

    try:
        # Step 1: Validate sender accounts
        logger.info("\n" + "="*50)
        logger.info("STEP 1: Validating sender accounts")
        logger.info("="*50)

        validated_accounts = validate_sender_accounts(accounts)
        if not validated_accounts:
            logger.error("Failed to validate sender accounts. Aborting.")
            return 1

        # Step 2: Load recipient emails
        logger.info("\n" + "="*50)
        logger.info("STEP 2: Loading recipient emails")
        logger.info("="*50)

        recipient_emails = load_recipient_emails(emails_json_file)
        if not recipient_emails:
            logger.error("Failed to load recipient emails. Aborting.")
            return 1

        # Step 3: Get email content templates
        logger.info("\n" + "="*50)
        logger.info("STEP 3: Loading email content templates")
        logger.info("="*50)

        templates = get_email_content_templates(not production_mails)  # Invert for is_warm_up
        if not templates:
            logger.error("Failed to load email content templates. Aborting.")
            return 1

        # Step 4: Schedule emails
        logger.info("\n" + "="*50)
        logger.info("STEP 4: Scheduling emails")
        logger.info("="*50)

        # Determine start date
        schedule_start_date = start_date or datetime.date.today()
        logger.info(f"Starting scheduling from: {schedule_start_date}")

        # Schedule the emails
        stats = schedule_emails(
            validated_accounts,
            recipient_emails,
            templates,
            daily_limits,
            schedule_start_date,
            dry_run
        )

        # Step 5: Display results
        logger.info("\n" + "="*50)
        logger.info("STEP 5: Results")
        logger.info("="*50)

        print_summary(stats)

        # Save dry run results if in dry run mode
        if dry_run:
            save_dry_run_results(stats)
            logger.info("\nThis was a DRY RUN. No emails were actually scheduled.")
            logger.info("To actually schedule emails, set dry_run = False in the script.")
        else:
            logger.info(f"\nSuccessfully scheduled {stats['emails_scheduled']} emails!")

        return 0

    except Exception as e:
        logger.error(f"Script failed with error: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())