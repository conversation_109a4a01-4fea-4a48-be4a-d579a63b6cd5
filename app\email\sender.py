#!/usr/bin/env python3
"""
Email sending functionality using Zoho Mail
"""
import ssl
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import <PERSON><PERSON>, Optional

from app.config import EMAIL_CONFIG
from app.utils.logger import setup_logger
from app.tracking.pixels import generate_tracking_id, add_tracking_pixel
from app.tracking.database import record_email_sent
from app.database.supabase_client import db

# Set up logger
logger = setup_logger('email.sender')

def extract_name_from_email(email_address: str) -> str:
    """
    Extract the name from an email address and format it with proper capitalization.

    Args:
        email_address (str): Email address like '<EMAIL>'

    Returns:
        str: Formatted name with first letter capitalized, e.g., '<PERSON><PERSON>z'
    """
    try:
        # Check if email address is valid and contains @
        if not email_address or '@' not in email_address:
            logger.warning(f"Invalid email address: '{email_address}'")
            return "Name"

        # Extract the part before @ symbol
        name_part = email_address.split('@')[0]

        # Check if name part is not empty
        if not name_part:
            logger.warning(f"Empty name part in email: '{email_address}'")
            return "Name"

        # Capitalize the first letter and make the rest lowercase
        formatted_name = name_part.capitalize()

        logger.debug(f"Extracted name '{formatted_name}' from email '{email_address}'")
        return formatted_name
    except Exception as e:
        logger.warning(f"Error extracting name from email '{email_address}': {str(e)}")
        return "Name"  # Fallback to generic "Name"

def process_email_body(body: str, sender_email: str) -> str:
    """
    Process the email body to replace [Name] placeholder with the actual sender's name.

    Args:
        body (str): Original email body containing [Name] placeholder
        sender_email (str): Sender's email address

    Returns:
        str: Processed email body with [Name] replaced by actual name
    """
    try:
        # Extract the name from the sender's email
        sender_name = extract_name_from_email(sender_email)

        # Replace [Name] with the actual name
        processed_body = body.replace('[Name]', sender_name)

        logger.debug(f"Replaced [Name] with '{sender_name}' in email body")
        return processed_body
    except Exception as e:
        logger.warning(f"Error processing email body: {str(e)}")
        return body  # Return original body if processing fails

def send_email(recipient: str, subject: str, body: str,
             email_id: Optional[str] = None, account_config: Optional[dict] = None) -> Tuple[bool, str]:
    """
    Send an email using SMTP_SSL with account-specific configuration.

    Args:
        recipient (str): Email address of the recipient
        subject (str): Subject of the email
        body (str): HTML body of the email
        email_id (str, optional): ID of the email in the database
        account_config (dict, optional): Account-specific configuration. If None, uses global EMAIL_CONFIG

    Returns:
        tuple: (success, tracking_id or error message)
    """
    # Use account-specific configuration if provided, otherwise fall back to global config
    if account_config:
        smtp_server = account_config['smtp_server']
        smtp_port = account_config['smtp_port']
        sender_email = account_config['sender_email']
        app_password = account_config['app_password']
        track_opens = account_config['track_opens']
        use_ssl = account_config.get('use_ssl', True)
        use_tls = account_config.get('use_tls', False)
        sender_name = account_config.get('sender_name', sender_email.split('@')[0])
        logger.info(f"Using account-specific configuration for {sender_email}")
    else:
        # Fall back to global configuration for backward compatibility
        smtp_server = EMAIL_CONFIG['smtp_server']
        smtp_port = EMAIL_CONFIG['smtp_port']
        sender_email = EMAIL_CONFIG['sender_email']
        app_password = EMAIL_CONFIG['app_password']
        track_opens = EMAIL_CONFIG['track_opens']
        use_ssl = not EMAIL_CONFIG.get('use_tls', False)  # SSL is opposite of TLS
        use_tls = EMAIL_CONFIG.get('use_tls', False)
        sender_name = EMAIL_CONFIG.get('sender_name', sender_email.split('@')[0])
        logger.info(f"Using global configuration for {sender_email}")

    # Get or generate a tracking ID
    tracking_id = None

    # If email_id is provided, try to get the tracking ID from the database
    if email_id:
        try:
            email_data = db.get_email_by_id(email_id)
            if email_data and email_data.get('tracking_id'):
                tracking_id = email_data.get('tracking_id')
                logger.info(f"Using existing tracking ID from database: {tracking_id}")
        except Exception as e:
            logger.error(f"Error retrieving tracking ID from database: {str(e)}")

    # If no tracking ID was found, generate a new one
    if not tracking_id:
        tracking_id = generate_tracking_id()
        logger.info(f"Generated new tracking ID: {tracking_id}")

    # Process email body to replace [Name] with sender's name
    body = process_email_body(body, sender_email)

    # Add tracking pixel if tracking is enabled
    if track_opens:
        body = add_tracking_pixel(body, tracking_id)

    # Check if app password is set
    if not app_password:
        logger.warning("App password is not set. Running in simulation mode.")
        logger.warning("To send real emails, provide your email provider's password as the first argument:")
        logger.warning("python main.py YOUR_APP_PASSWORD")

        # For testing, we'll simulate a successful send
        logger.info(f"SIMULATION MODE: Email would be sent to {recipient}, Tracking ID: {tracking_id}")

        # Record the email in both tracking databases
        if track_opens:
            # Record in in-memory database
            record_email_sent(tracking_id, recipient, subject)

            # Record in Supabase database if email_id is provided
            if email_id:
                try:
                    # We already have email_data from when we retrieved the tracking ID
                    if not 'email_data' in locals() or not email_data:
                        email_data = db.get_email_by_id(email_id)

                    if email_data:
                        recipient_id = email_data.get('recipient_id')

                        # Create initial tracking record in Supabase
                        tracking_record = db.create_email_tracking_record(email_id, tracking_id, recipient_id)
                        if tracking_record:
                            logger.info(f"Created initial tracking record in Supabase for email ID: {email_id}")
                        else:
                            logger.warning(f"Failed to create initial tracking record in Supabase for email ID: {email_id}")
                    else:
                        logger.warning(f"Could not find email data for ID: {email_id}")
                except Exception as e:
                    logger.error(f"Error creating initial tracking record in Supabase: {str(e)}")

        # Follow-up handling is now done directly through the database

        return True, tracking_id

    # Create message - using MIMEMultipart to support HTML and tracking pixels
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = recipient
    message["Subject"] = subject

    # Generate a unique Message-ID for this email
    from email.utils import formatdate, make_msgid

    # Generate a Message-ID
    # Note: Message-ID storage in database has been removed as part of reply tracking removal
    message_id = make_msgid(domain=sender_email.split('@')[1])
    logger.info(f"Generated new Message-ID: {message_id}")

    # Add Message-ID header
    message["Message-ID"] = message_id

    # Add Date header
    message["Date"] = formatdate(localtime=True)

    # If this is a follow-up email, add References and In-Reply-To headers
    if email_id:
        try:
            email_data = db.get_email_by_id(email_id)
            if email_data and email_data.get('is_follow_up') and email_data.get('previous_email_id'):
                # Previous email threading code removed
                pass
        except Exception as e:
            logger.error(f"Error processing email data: {str(e)}")

    message.attach(MIMEText(body, "html"))

    # Create a secure SSL context
    context = ssl.create_default_context()

    server = None
    try:
        # Use account-specific SSL/TLS settings
        if use_ssl:
            logger.info(f"Connecting to SMTP server {smtp_server}:{smtp_port} using SSL...")
            server = smtplib.SMTP_SSL(smtp_server, smtp_port, context=context)
        else:
            logger.info(f"Connecting to SMTP server {smtp_server}:{smtp_port} using TLS...")
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls(context=context)

        # Login
        logger.info(f"Logging in as {sender_email}...")
        server.login(sender_email, app_password)
        logger.info("✓ Login successful!")

        # Send email
        logger.info(f"Sending email to {recipient}...")
        server.send_message(message)

        # Record the email in both tracking databases
        if track_opens:
            # Record in in-memory database
            record_email_sent(tracking_id, recipient, subject)

            # Record in Supabase database if email_id is provided
            if email_id:
                try:
                    # We already have email_data from when we retrieved the tracking ID
                    if not 'email_data' in locals() or not email_data:
                        email_data = db.get_email_by_id(email_id)

                    if email_data:
                        recipient_id = email_data.get('recipient_id')

                        # Create initial tracking record in Supabase
                        tracking_record = db.create_email_tracking_record(email_id, tracking_id, recipient_id)
                        if tracking_record:
                            logger.info(f"Created initial tracking record in Supabase for email ID: {email_id}")
                        else:
                            logger.warning(f"Failed to create initial tracking record in Supabase for email ID: {email_id}")
                    else:
                        logger.warning(f"Could not find email data for ID: {email_id}")
                except Exception as e:
                    logger.error(f"Error creating initial tracking record in Supabase: {str(e)}")

        logger.info(f"✓ Email sent successfully to {recipient}, Tracking ID: {tracking_id}")

        # Follow-up handling is now done directly through the database

        # Close connection
        server.quit()
        logger.info("Connection closed")

        return True, tracking_id

    except Exception as e:
        logger.error(f"✗ Error: {str(e)}")
        logger.error(f"Full error details: {repr(e)}")

        # Always close the connection, even if an error occurred
        if server:
            try:
                server.quit()
                logger.info("SMTP connection closed")
            except Exception:
                # If we can't quit gracefully, don't worry about it
                pass

        return False, f"Error: {str(e)}"

def test_zoho_auth():
    """
    Test Zoho Mail authentication separately using the simplified approach.

    Returns:
        tuple: (success, message)
    """
    smtp_server = EMAIL_CONFIG['smtp_server']
    smtp_port = EMAIL_CONFIG['smtp_port']
    sender_email = EMAIL_CONFIG['sender_email']
    app_password = EMAIL_CONFIG['app_password']

    if not app_password:
        logger.error("No app password provided. Cannot test authentication.")
        return False, "No app password provided"

    logger.info(f"Testing Zoho authentication with: {sender_email}")
    logger.info(f"Password length: {len(app_password)}")
    logger.info(f"SMTP Server: {smtp_server}:{smtp_port}")

    # Create a secure SSL context
    context = ssl.create_default_context()

    server = None
    try:
        # Using the simplified approach from test_zoho2.py
        logger.info(f"Connecting to SMTP server {smtp_server}:{smtp_port} using SSL...")
        server = smtplib.SMTP_SSL(smtp_server, smtp_port, context=context)

        # Login
        logger.info(f"Logging in as {sender_email}...")
        server.login(sender_email, app_password)
        logger.info("✓ Authentication successful!")

        # Close connection
        server.quit()
        logger.info("Connection closed")

        return True, "Authentication successful"

    except Exception as e:
        logger.error(f"✗ Error: {str(e)}")
        logger.error(f"Full error details: {repr(e)}")

        # Always close the connection, even if an error occurred
        if server:
            try:
                server.quit()
                logger.info("SMTP connection closed")
            except Exception:
                # If we can't quit gracefully, don't worry about it
                pass

        return False, f"Error: {str(e)}"
